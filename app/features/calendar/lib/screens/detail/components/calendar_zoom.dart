import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/widgets/gradient_box_border.dart';
import 'package:gp_core/widgets/gradient_text.dart';

class CalendarMeet extends StatefulWidget {
  final String meetingUrl;
  final String meetingId;
  final DateTime startTime;
  final DateTime endTime;
  final bool isAllday;
  final bool enabled;

  const CalendarMeet({
    super.key,
    required this.startTime,
    required this.meetingId,
    required this.meetingUrl,
    required this.isAllday,
    required this.endTime,
    this.enabled = true,
  });

  @override
  State<CalendarMeet> createState() => _CalendarMeetState();
}

class _CalendarMeetState extends State<CalendarMeet> {
  Timer? _timer;
  final _text = BehaviorSubject.seeded('');
  final bool _isOver = false;

  @override
  void initState() {
    super.initState();

    //
    // _text.add(_calculate(widget.startTime, widget.endTime));

    // start timer
    // _timer?.cancel();
    // _timer = null;
    // const duration = Duration(seconds: 1);
    // _timer = Timer.periodic(duration, (timer) {
    //   final newText = _calculate(widget.startTime, widget.endTime);
    //   _text.add(newText);
    // });
  }

  @override
  void dispose() {
    // _timer?.cancel();
    // _timer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            const SvgWidget('assets/images/svg/gg-meet.svg',
                width: 24, height: 24),
            const SizedBox(width: 16),
            Flexible(
              fit: FlexFit.tight,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        LocaleKeys.calendar_details_zoom_title.tr,
                        style: textStyle(GPTypography.headingSmall),
                      ),
                      const SizedBox(width: 8),
                      AIAssistantChip(),
                    ],
                  ),
                  Text(
                    widget.meetingUrl,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: textStyle(GPTypography.bodyMedium)?.copyWith(
                        color: GPColor.contentSecondary,
                        fontWeight: FontWeight.w400),
                  ),
                  // StreamBuilder<String>(
                  //     stream: _text.stream,
                  //     builder: (context, snapshot) {
                  //       return Text(
                  //         // widget.isAllday
                  //         //     ? LocaleKeys
                  //         //         .calendar_details_zoom_during_all_day_label.tr
                  //         //     :
                  //         snapshot.data ?? '',
                  //         style: textStyle(GPTypography.bodyMedium)?.copyWith(
                  //             color: GPColor.contentSecondary,
                  //             fontWeight: FontWeight.w400),
                  //       );
                  //     }),
                ],
              ),
            ),
            Visibility(
              visible: !_isOver,
              child: CupertinoButton(
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: widget.meetingUrl))
                        .then((_) {
                      Popup.instance.showSnackBar(
                        message: LocaleKeys.task_longPress_copied.tr,
                        type: SnackbarType.success,
                      );
                    });
                  },
                  padding: const EdgeInsets.all(8),
                  // color: GPColor.bgSecondary,
                  minSize: 0,
                  child: const SvgWidget(
                      'assets/images/svg/ic16-line15-2square.svg')),
            ),
          ],
        ),
        const SizedBox(height: 12),
        GPWorkButton(
          title: LocaleKeys.calendar_details_zoom_button_join.tr,
          isEnabled: !_isOver && widget.enabled,
          onTap: () => Utils.openMeet(
            meetingUrl: widget.meetingUrl,
          ),
        ),
      ],
    );
  }

  // String _calculate(DateTime dateTime, DateTime endTime1) {
  //   var text = '';

  //   final now = DateTime.now();
  //   if (endTime1.isBefore(now)) {
  //     text = LocaleKeys.calendar_details_zoom_label_meeting_is_ended.tr;
  //     // _isOver = true;
  //     return text;
  //   }

  //   final duration = dateTime.difference(now);
  //   if (duration.inSeconds <= 0) {
  //     text += LocaleKeys.calendar_details_zoom_label_in_meeting.tr;
  //   } else if (duration.inMinutes <= 59) {
  //     text += LocaleKeys.calendar_details_zoom_start_after.tr +
  //         Utils.addSIfNeeded(duration.inMinutes);
  //     text += ' ${duration.inMinutes + 1} ';
  //     text += LocaleKeys.calendar_details_remind_minute.tr;
  //     text += ' ' + LocaleKeys.calendar_details_zoom_nua.tr;
  //   } else if (duration.inHours <= 23) {
  //     text += LocaleKeys.calendar_details_zoom_start_after.tr;
  //     text += ' ${duration.inHours} ';
  //     text += LocaleKeys.calendar_details_remind_hour.tr +
  //         Utils.addSIfNeeded(duration.inHours);
  //     text += ' ' + LocaleKeys.calendar_details_zoom_nua.tr;
  //     // } else if (duration.inDays == 1) {
  //     //   text += LocaleKeys.calendar_details_zoom_start_on.tr +
  //     //       ' ' +
  //     //       LocaleKeys.calendar_details_zoom_tomorrow.tr;
  //     // } else if (duration.inDays == 2) {
  //     // text += LocaleKeys.calendar_details_zoom_start_on.tr +
  //     //     ' ' +
  //     //     LocaleKeys.calendar_details_zoom_day_after_tomorrow.tr;
  //   } else {
  //     final formatter =
  //         DateFormat('HH:mm E, dd/MM/yyyy', Get.locale?.languageCode ?? 'vi');
  //     text = LocaleKeys.calendar_details_zoom_start_on.tr +
  //         ' ${formatter.format(dateTime)}';
  //   }
  //   return text;
  // }
}

class AIAssistantChip extends StatelessWidget {
  const AIAssistantChip({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
        decoration: BoxDecoration(
          border: GradientBoxBorder(
              gradient: const LinearGradient(
            colors: [Color(0xFF8B74FF), Color(0xFFFBA446)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          )),
          borderRadius: BorderRadius.circular(100),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(72),
                  child: Image.asset(
                    Assets
                        .PACKAGES_GP_ASSETS_IMAGES_AI_BOT_ASSISTANT_AVATAR_PNG,
                  ),
                ),
              ),
              const SizedBox(width: 4),
              GradientText(
                  LocaleKeys
                      .calendar_ai_meeting_chip_title.tr,
                  style: textStyle(GPTypography.headingSmall)
                      ?.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          height: 1.3),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF8B74FF),
                      Color(0xFFFBA446)
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  )),
              const SizedBox(width: 4),
            ],
          ),
        ));
  }
}
