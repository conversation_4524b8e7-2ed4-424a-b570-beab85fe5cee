{"album": {"albumLongPress": {"accessDenied": "You do not have permission to delete this task!", "addImage": "Add Image/ Video", "copy": "Copy url", "delete": "Delete album", "deleteContent": "All images and videos in this album will be delete permanently. Do you want to delete this album?", "deleted": "Album and all media has been deleted!", "edit": "Edit information", "notExists": "Album doesn't exist", "turnOffNotify": "Turn off album notification", "turnOnNotify": "Turn on album notification"}, "create": {"create_button": "Create album", "desc_placeholder": "Enter album description...", "name_placeholder": "Enter album name...", "privacy": {"friend": "Friends", "in_workspace": "In organization", "only_me": "Only me", "title": "Privacy"}, "title": "Create new album"}, "detail": {"add_new": "Add Picture/Video", "bottom_sheet_copy_url": "Copy link", "bottom_sheet_share_post": "Write post", "bottom_sheet_title": "Share album", "comment": "Comment", "empty_asset": "Album is empty!", "like": "Like", "share": "Share", "snack_bar_save_file_fail": "file is saved unsuccessfully!", "snack_bar_save_file_success": "Your file is saved successfully!", "snack_bar_save_image_fail": "Image is saved unsuccessfully!", "snack_bar_save_image_success": "Image is saved successfully!", "snack_bar_save_video_fail": "Video is saved unsuccessfully!", "snack_bar_save_video_success": "Video is saved successfully!"}, "edit": {"save": "Save", "title": "Edit album"}, "list": {"addImage": "Add Image/ Video", "album": "Album", "allImage": "All image", "child": "images", "image": "Image", "imageEmpty": "No images has been uploaded!"}, "mediaLongPress": {"accessDenied": "You do not have permission to delete this %1s!", "copy": "Copy %1s url", "delete": "Delete this %1s", "deleteConfirm": "This %1s will be delete permanent, Do you want to delete this %2s?", "deleted": "%1s has been deleted!", "download": "Download", "downloadError": "Can not download this %1s", "downloaded": "Your %1s has been downloaded!", "image": "image", "notExists": "%1s is not exists!", "video": "video"}}, "alert": {"cancel": "Cancel", "dismiss": "Cancel", "done": "Done", "later": "Later", "ok": "OK", "skip": "<PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "archive": {"archiveFolderSuccess": "Folder has been archived", "archiveProjectSuccess": "Project has been archived", "archiveTaskListSuccess": "Task list has been archived", "archiveTaskSuccess": "Task has been archived", "confirmArchiveFolder": {"content": "Are you sure want to archive this folder? This folder can be restore from", "content1": "on the project list screen.", "title": "Archive folder"}, "confirmArchiveProject": {"content": "Are you sure want to archive this project? This project can be restore from", "content1": "on the project list screen.", "title": "Archive project"}, "confirmArchiveTask": {"content": "Are you sure want to restore this task? This task will be restored", "content1": "on the project list screen.", "title": "Archive task"}, "confirmArchiveTaskList": {"content": "Are you sure want to archive this task list? This task list can be restore from", "content1": "on the project list screen.", "title": "Archive task list"}, "confirmUnArchiveFolder": {"content": "Are you sure to restore this folder? All tasks and task lists in this folder will be restored.", "title": "Restore folder"}, "confirmUnArchiveProject": {"content": "Are you sure to restore this project? All folders, task lists and tasks of this project will be restored.", "title": "Restore project"}, "confirmUnArchiveTask": {"content": "Are you sure want to restore this task? This task will be restored", "title": "Restore task"}, "confirmUnArchiveTaskList": {"content": "Are you sure want to restore this task list? This task list and all tasks it contains will be restored", "title": "Restore task list"}, "emptyProjectTitle": "Nothing here", "emptyTitle": "No archive here", "title": "Archive", "unarchiveFolderSuccess": "Folder has been restored", "unarchiveProjectSuccess": "Project has been restored", "unarchiveTaskListSuccess": "Task list has been restored", "unarchiveTaskSuccess": "Task has been restored"}, "calendar": {"ai_meeting": {"calendar_box": {"main_function": "🚀 The AI assistant will capture key points from the meeting", "remaining_count": "🕛 Joins remaining:", "reset_time": "⌛ The meeting count will reset at 00:00 on", "title": "AI assistant to the meeting", "tooltip": "The Meeting Assistant is not supported for recurring meetings"}, "chip_title": "AI assistant", "info_page": {"contact_title": "🚀 CONTACT US", "description_1": "The AI automatically records and consolidates the meeting into a clear summary.", "description_2": "Notes are sent to all attendees, ensuring everyone has complete and accurate information.", "description_bold_1": "Smart summaries: ", "description_bold_2": "Easy sharing: ", "title": "AI GapoWork\nIntelligent meeting assistant", "understand": "Understand"}}, "all": "All", "and": "and", "button_add_email": "Add emails", "button_add_invitee": "Add participants", "button_appbar_cancel": "Cancel", "button_create": "Create", "calendar_comparison": "Calendar comparison", "calendar_label_repeat_monday_to_friday": "Repeat every weekday", "calendar_notifications": "calendar notifications", "cancel_sync_google_calendar_success": "Successfully canceled sync with Google calendar", "checking_calendars_with": "Checking calendars with", "collab": {"active_meeting_button": "Enable collab meeting", "back": "Back", "created_snackbar_message": "Meeting “%1s” was created successfully", "discard": "Discard", "discard_editing_message": "The content you have just changed will be lost and cannot be recovered", "edited_snackbar_message": "Meeting content “%1s” has been changed", "unable_to_active_label": "This function can only be activated by collab group owner"}, "confirmDelete": {"accessDenied": "You do not have permission to delete this %1s!", "cancel": "Close", "delete": "Delete", "deleteAppointment": "Delete %1s?", "deleteAppointmentContent": "Are you sure want to delete %1s “%2s”?", "deleteRecursiveTitle": "This and all following %2ss", "deleteSuccess": "%1s has been deleted!"}, "confirm_to_unsync_google_calendar": "Are you sure you want to cancel sync your Google Calendar account?", "create": {"add_host": "Add hosts", "appbar_title": "New meeting", "appbar_title_create_event": "Create meeting", "appbar_title_create_reminder": "Create reminder", "appbar_title_create_todo": "Create todo", "attachmentfiles": {"button_add": "Upload", "title": "Attachment files", "title_add": "Add attachment files"}, "button_back_discard_event": "Close", "button_confirm_discard_event": "Discard", "event_title_textfield_placeholder": "Meeting title", "event_type": {"empty": "No event type has been defined yet", "not_selected": "Select event type", "title": "Event type"}, "label_all_day": "All day event", "label_attachment_file": "Attachment files", "label_edit_event_content": "Edit", "label_end": "End", "label_event_content": "Meeting description", "label_event_content_placeholder": "Tap to add description...", "label_meet": "Online meeting via Google Meet", "label_participants": "Participants", "label_remind": "Remind", "label_repeat": "Repeat", "label_save": "Save", "label_start": "Start", "label_todo_content": "Todo description", "label_zoom": "Zoom Meeting", "message_discard_event": "If you discard this event, all the data you filled will be lost.", "not_google_signed_in": "You will be directed to sign in to Google after creating the meeting to complete the initialization", "not_grant_scope_google_description": "Please grant all permission scopes to create meetings", "not_grant_scope_google_drive_description": "Please grant all permission scopes to view files on Google Drive", "not_grant_scope_google_title": "You have not given enough permission scope", "placeholder_description_page": "What would you like to share?", "popup_button_edit_event_back": "Close", "popup_button_edit_event_save": "Save", "popup_checkbox_title_edit_event": "This and subsequent events", "popup_description_edit_event": "Are you sure you want to save the event's changes", "popup_title_edit_event": "Edit event", "recursive_event_all": "All %1s", "recursive_event_only": "This %1s", "recursive_event_this_next": "This %1s and all following %2ss", "title_description_page": "Meeting description", "title_discard_event": "Discard this event?", "title_edit_event": "Edit %1s"}, "currently_you_have_no_announcement": "Currently you have no notification", "deleted_event": {"close_btn": "Close", "description": "You will not be able to access or view the content of this event", "not_member_title": "You are no longer a member of this event", "title": "Can't view this event"}, "details": {"accept": "Attend", "accept_bottomsheet_title": "Attend event", "appbar_title_event": "Meeting", "appbar_title_reminder": "Reminder", "appbar_title_todo": "Todo", "approval_time": "Request time", "attendee_list": "Participants", "button_add_people": "Add", "button_title_mark_as_completed": "Mark complete", "button_title_mark_as_uncompleted": "<PERSON> uncomplete", "confirm_title": "Attendance confirmation", "denied": "<PERSON><PERSON>", "deny_bottomsheet_cancel": "Cancel", "deny_bottomsheet_confirm": "Confirm", "deny_bottomsheet_title": "Deny event", "email_title": "Participants from email", "num_accepted": "Attend", "num_attendee": "%1 participants", "num_denied": "<PERSON><PERSON>", "num_host": "%1 hosts", "participants": {"display_on_detail": "List participants from email", "title": "Participants"}, "remind": {"day": "day", "hour": "hour", "label": "Remind", "label_before_1_day_on": "Remind before 1 day at", "label_on": "Remind on this day at", "minute": "minute"}, "repeat": {"everyday": "Every day", "everymonth": "every month", "everyweek": "every week", "fifth": "fifth", "first": "first", "fourth": "fourth", "friday": "Friday", "friday_number": "Friday", "label_daily": "Repeat every day", "label_monthly": "Repeat every month", "label_weekday_with_week_position": "On the % % every month", "label_weekly": "Repeat every week", "label_yearly": "Repeat every year", "monday": " Monday", "monday1": "Monday", "mondayToFriday": "Monday to friday", "monday_number": "Monday", "saturday": "Saturday", "saturday_number": "Saturday", "second": "second", "sixth": "sixth", "sunday": "Sunday", "third": "third", "thursday": "Thursday", "thursday_number": "Thursday", "tuesday": "Tuesday", "tuesday_number": "Tuesday", "wednesday": "Wednesday", "wednesday_number": "Wednesday", "weekday_with_week_position": "Repeat on the % % every month"}, "room": {"at": "At"}, "room_approval": {"accept": "Approve", "appbar_title": "Room", "approved": "Approved", "approved_by": "Approved by", "approver": "Approver list", "cancel": "Cancel", "denied": "Rejected", "deny": "Reject", "popup": {"accept_description": "Requests for the same meeting room and time will be automatically rejected. Are you sure to approve this request?", "accept_title": "Confirm approval", "deny_description": "Are you sure to reject this request?", "deny_title": "Confirm rejection"}, "snackbar": {"accept": "Approved room booking request", "deny": "Rejected room booking request"}, "title": "Room booking approval", "waiting": "Waiting for approval"}, "room_approval_title": "Room booking approval", "snackbar_add_attendee_success": "Add attendee success", "zoom": {"button_join": "Join online meeting room", "day_after_tomorrow": "day after tomorrow", "during_all_day_label": "Open all the day", "label_in_meeting": "This event has started", "label_meeting_is_ended": "The event is over", "nua": "", "start_after": "Start after", "start_on": "Start on", "title": "Meeting", "tomorrow": "tomorrow"}}, "done": "Done", "drag_drop": {"button_cancel": "Cancel", "button_send": "Send", "label_notify_to_attendees_message": "Send notification to attendees?", "undo_title": "Undo", "update_success_message": "Moved to %,"}, "email": {"discard_editing": "Discard editing", "discard_editing_des": "Are you sure you want to cancel these edits? any data you entered will not be saved.", "understood": "Understood", "wrong_email_format": "Email address is not valid", "wrong_email_format_des": "Please enter the valid email address."}, "end": {"after": "After", "at": "On a day", "never": "Never"}, "error": {"emptyTitle": "Content does not exist", "endTimeBeforeStartTime": "End time can not before Start time ", "noPermissionAccess": "Or has no access rights", "noPermissionAccessMsg": "You do not have permission to view this", "openList": "Go to your calendar"}, "eventMultipleAttendee": "Event participants", "filter_event": "EVENT FILTER", "gg_calendar": "Google Calendar", "google_drive": {"no_permission": {"accept": "Request access", "cancel": "Later", "content": "Please request access with your Google account", "title": "You do not have permission to access this attachment"}}, "host": "Hosts", "hosts": "Hosts", "label_add_zoom_room": "Add zoom meeting", "label_all_day": "All day", "label_all_day_2": "This is an all-day %1s", "label_list_invitees": "List invitees", "label_no_remind_before": "No remind", "label_no_repeat": "No repeat", "label_remind_before_days": "Remind before % day", "label_remind_before_days_on": "Remind before % day at % AM", "label_remind_before_hours": "Remind before % hour", "label_remind_before_minutes": "Remind before % minutes", "label_remind_this_day_on": "Remind on this day at % AM", "label_repeat_custom": "Customize...", "label_repeat_custom_title": "Config recurrence", "label_repeat_daily": "Repeat every day", "label_repeat_end": "End time", "label_repeat_every": "Repeat every", "label_repeat_monthly": "Repeat every month on %1s", "label_repeat_weekly": "Repeat every week on %1s", "label_title_event": "Event", "label_zoom": "Add zoom meeting", "longPress": {"deleteAppointment": "Delete this %1s", "edit": "Edit this event information", "editAppointment": "Edit this %1s", "edit_appointment_bottom_sheet_title": "Edit this %1s information", "markComplete": "Mark as completed"}, "meeting_room": "Meeting room", "ms_outlook": "Ms Outlook", "nameTag": "Tag name", "notification_title": "Calendar notifications", "other_people": "other people", "refreshSuccess": "Your calendar has been updated", "repeat": {"by_day": "Day", "by_month": "Month", "by_week": "Week", "by_year": "Year"}, "repeat_by_month_on_a_day": "On %1s every month", "repeat_by_month_on_a_weekday": "On %2s %1s every month", "repeat_by_month_on_last_weekday": "On last %1s every month", "repeat_by_year_on_a_day": "On %1s every year", "repeat_by_year_on_a_weekday": "On %2s %1s of %3s every year", "repeat_display": {"after": "after %1s times", "daily": "Repeat every %1s days", "daily_once": "Repeat every day", "end_at": "until %1s", "monthly_on_a_day": "Repeat every %1smonths on %2s", "monthly_on_a_day_once": "Repeat every month on %1s", "monthly_on_a_weekday": "Repeat every %1smonths on %3s %2s", "monthly_on_a_weekday_once": "Repeat every month on %2s %1s", "monthly_on_a_weekday_weekOrdinal": "Repeat on %2s %1s every month", "monthly_on_last_weekday": "Repeat every %1smonths on last %2s", "monthly_on_last_weekday_once": "Repeat every month on last %1s", "repeat_on_day_of_week": "Repeat on", "weekly": "Repeat every%1sweek on %2s", "weekly_once": "Repeat every%1sweeks", "weekly_once_on_weekday": "Repeat every week on %1s", "yearly": "Repeat every %1s year", "yearly_on_a_day": "Repeat every %1syears on %3s %2s", "yearly_on_a_day_once": "Repeat every year on %2s %1s", "yearly_on_a_weekday": "Repeat every %1syears on %3s %2s of %4s", "yearly_on_a_weekday_once": "Repeat every year on %2s %1s of %3s"}, "room": {"add": "Add", "all_area": "All", "all_room_busy_all_day_event_title": "All meeting room are busy", "all_room_busy_description": "Please choose another time slot to book the meeting room", "all_room_busy_title": "All meeting room are busy in this time slot from %1 to %2", "approver": "Approval", "area": "Area", "available": "Free", "busy": "Busy", "edit_time_room_busy_description": "Please choose another time to rebook the meeting room", "edit_time_room_busy_title": "This meeting room is busy in this time slot from %1 to %2", "empty_area": "Don't have any area", "empty_room": "Don't have any meeting room", "empty_room_search": "No result", "meeting_room": "Meeting room", "no_room_description": "Your organization doesn't have a meeting room on GapoWork yet.", "no_room_selected": "No room chosen", "no_room_title": "No meeting rooms created yet", "search_area_hint": "Search area", "warning_message": "Repeat options are not available in meeting room booking", "warning_message_multi_day": ""}, "room_tab": {"booking_room_tab": "Meeting room", "busy_room": "Room busy", "busy_room_snackbar": "The meeting room has been booked for the time slot you selected. Please choose another time slot", "calendar_tab": "My calendar", "change_room": "Change", "not_have_any_room_description": "Your organization doesn't have any meeting rooms on GapoWork.", "not_have_any_room_title": "No meeting rooms", "search_hint": "Search"}, "select_invitees": {"button_continue": "Continue", "button_members_list_detail": "Members detail", "button_more_members_detail": "See more members", "button_select_all": "Select all", "button_title_view_attendees": "View %1s attendees", "group_cell_member": "members", "is_added_by_addmin_str": "Added by <PERSON><PERSON>", "pick_all_department_title": "All departments", "placeholder_search": "Search", "search_organization": "Search organization", "tab_title_bot": "<PERSON><PERSON><PERSON>", "tab_title_department": "Departments", "tab_title_group": "Group", "tab_title_member": "Members", "tab_title_role": "Roles", "tab_title_workspace": "Organization", "title_invite": "Invite"}, "separator_________________________": "--------------------------", "snack_bar_message_create_event_successfully": "Created event successfully", "sync": {"google_grant_scope": {"action": "Go to Google scope", "message": "For syncing events between Google and GapoWork, you need to trust GapoWork and grant Google scope that Gapowork can access", "title": "Select Google scope"}, "google_not_sync_event": "Not synced with Google Calendar", "google_not_sync_event_detail_tooltip": "This event cannot sync Google Calendar for the guests because the creator has not synced their Google Calendar.", "google_not_sync_event_tooltip": "This event cannot sync Google Calendar for the guests because the creator has not synced their Google Calendar.", "google_not_sync_header": "Not synced with Google Calendar", "google_sync_button": "Sync calendar with Google Calendar", "google_title_bottom_sheet_sync_again": "Resync to Google calendar ?", "google_title_sync_expired": "Google calendar has expired", "outlook_bottom_sheet_filter_button_gapowork_title": "GapoWork", "outlook_bottom_sheet_filter_button_google_title": "Google Event", "outlook_bottom_sheet_filter_button_outlook_title": "MS Outlook Event", "outlook_bottom_sheet_filter_section_title": "MY EVENT", "outlook_button_bottom_sheet_sync_again": "Resync", "outlook_button_bottom_sheet_unsync": "Cancel sync", "outlook_description_bottom_sheet_sync_again": "The synchronization has expired, please re-sync to continue", "outlook_description_bottom_sheet_sync_expired": "MS Outlook has expired", "outlook_description_bottom_sheet_unsync": "Do you want to cancel sync to Microsoft Outlook?", "outlook_sync_button": "Sync calendar with MS Outlook", "outlook_title_bottom_sheet_button_sync": "Sync with MS Outlook", "outlook_title_bottom_sheet_section": "MS OUTLOOK CALENDAR ACCOUNT", "outlook_title_bottom_sheet_sync_again": "Resync to MS Outlook ?", "outlook_title_bottom_sheet_unsync": "Cancel sync", "outlook_title_button_bottom_sheet_later": "Later", "outlook_title_button_bottom_sheet_unsync": "Cancel sync", "outlook_title_button_sync_now": "Sync Now", "outlook_title_snackbar_unsync": "Cancel sync succesfully", "outlook_title_sync_expired": "MS Outlook has expired", "outlook_title_synced": "Calendar has been synced successfully", "outlook_title_syncing": "Syncing with MS Outlook", "outlook_title_unsynced": "The synchronization is canceled", "remove_google_event": "Remove all events from Google Calendar on GapoWork Calendar", "section_title": "Calendar sync", "sync_now": "Sync now"}, "sync_google_calendar_success": "Calendar has been synced successfully", "syncing_with_google_calendar": "Syncing with Google calendar", "textfield_description_placeholder": "Description for this %1s", "textfield_title_placeholder": "Title for this event", "textfield_title_placeholder_reminder": "Description for this reminder", "textfield_title_placeholder_todo": "Description for this todo", "time_disappear": "times", "title_attendees": "Guests", "type": {"meeting": "Event", "other": "Other", "reminder": "Reminder", "title_meeting": "Event", "title_other": "Other", "title_reminder": "Reminder", "title_todo": "Todo", "todo": "Todo"}, "unsync_google_calendar": "Unsycn Google Calendar", "until": "Until", "usync_button_title": "Unsycn", "will_be_displayed_here": "will be displayed here.", "you": "You"}, "coin": {"bottom_navigation_bar_gift": "Redeem gift", "bottom_navigation_bar_my_wallet": "My wallet", "bottom_navigation_bar_qr_code": "Scan QR", "deliver_person": "Sender", "gift_list": {"empty_description": "There are currently no gifts available for redemption.\nPlease check back later after your organization adds new rewards!"}, "gift_transaction": {"redeem_gift": "Redeem gift", "redeem_gift_canceled": "Redemption cancelled", "refund_point": "Points refunded"}, "qr": {"move_to_frame_to_capture": "Move the QR code to the camera center, it will be scanned automatically.", "qr_code_accept": "Confirm transaction", "qr_code_canceled": "This QR code has been canceled and cannot be used.", "qr_code_expired": "This QR code expired on", "qr_code_name_title": "QR code name", "qr_code_not_detected": "QR code not found. Please select a clear and fully visible QR code image.", "qr_code_not_effective": "This QR code is not yet valid. You can scan it again after", "qr_code_not_found": "Invalid QR code", "qr_code_scan_reach_limit": "This QR code has reached the maximum scan limit (%s). It cannot be used anymore.", "qr_code_user_reach_limit": "You have reached the maximum scan limit (%s). You cannot scan anymore.", "qr_coin_amount_received_title": "Points received", "qr_coin_amount_subtraction_title": "Points deducted", "qr_coin_successfully_received": "Points successfully received", "qr_coin_successfully_subtracted": "Points sucessfully deducted"}, "receiver": "Recipient", "scan_qr_title": "Scan QR Code", "select_photo": "Choose image", "trading": {"detail": {"reason": {"title": "Reason"}, "receive": {"title": "Transaction type"}, "status": {"failed": "Failed", "processing": "Processing", "success": "Successful"}, "time": "Time", "title": "Transaction details", "transaction": {"is_not_recall_point": "Receive points from organization", "recall_point": "Your points have been deducted", "type": "Transaction type"}, "transfer": {"organization": "Organization fund", "title": "Point source"}, "value": {"title": "Points"}}, "history": {"current_balance": "Accumulated points", "empty": {"content": "All transactions affecting your point balance will be displayed here", "title": "No transactions yet"}, "item": {"because": "because", "in": {"reason": "From organization fund because", "title": "Points received"}, "out": {"reason": "To organization fund because", "title": "Points deducted"}}}}, "understood": "Understood"}, "conversations_title": "Cha<PERSON>", "date": {"pick_time": "Pick time"}, "detail_text": "Detail", "drive": {"appbar": {"search": "", "title": {"bin": "Trash", "default": "Gapo Drive", "my": "My Drive", "search": "Search", "share": "Shared with me"}}, "assignee_picker": {"action_done": {"logs_permission": "Done", "share_files": "Next"}, "title_pick": {"logs_permission": "Add members", "share_files": "Add people"}}, "behavior": {"add": {"file": "Add file", "folder": "Add new folder", "image_or_video": "Add image/video", "url": ""}, "item": {"back_to_home": "Home", "copy_link": "Copy link", "create_shortcut": "Add shortcut", "delete": "Delete", "delete_permanent": "Delete permanent", "details": "View info and activity", "download": "Download", "move": "Move", "preview": "Preview", "rename": "<PERSON><PERSON>", "restore": "Rest<PERSON>", "share": "Share"}, "move": {"button_done": "Done", "search_empty_content": "Please check your search keywords", "search_empty_title": "No result found", "search_hint": "Search location", "tab": {"my": "Me", "shared": "Shared"}, "title": "Move"}, "roll_back": "Undo"}, "bin": {"delete_all": "Delete all", "hint": "All items in Trash will be pernamently deleted after 30 days"}, "empty": {"bin_drive": {"content": "Your delete items will be here", "title": "Empty"}, "my_drive": {"content": "Click \"+\" to start upload", "title": "You dont have any items yet"}, "search": {"appbar": {"title": "Search results"}, "content": "Please re-enter your search keyword", "title": "No matching results"}, "share_drive": {"content": "Items that others share with you will be displayed here", "title": "There are no items to share with you yet"}}, "file_management": {"common": {"default_folder_name": "The folder has no title yet", "file": "items", "file_hint": "%1s item", "handling": "Processing %1s item", "last_updated_at": "Last updated", "success": "Complete"}, "download": {"cancel": "Cancel download", "empty_data": "Empty", "title": "Download"}, "rename": {"folder": "Has renamed"}, "upload": {"cancel": "Cancel upload", "empty_data": "Empty", "title": "Upload"}}, "file_type": {"folder": "Folder", "photo": "Photo", "video": "Video"}, "logs": {"activity": {"filters": {"all": "All", "history": "History", "permission_changed": "Change Access Permission"}}, "details": {"created_date": "Created at ", "file_type": "Type", "headers": {"info": "Information", "user_permission": "Access Permission"}, "info": "Information", "last_modified_date": "Last Edited", "member": "Member", "owner": "Owner", "share_link": "Shared Link", "share_link_content": "Only member with access permission can open with  the link"}, "tab": {"activity": "Activity", "details": "Detail"}}, "popup": {"cancel_upload": {"cancel": "Cancel", "confirm": "Continue ", "content": "You still have items that haven't loaded successfully. Do you want to cancel?", "title": "Cancel uploading items"}, "create_folder": {"action": "Create", "input_hint": "Enter folder name", "input_name": "Folder name", "title": "Create new folder"}, "delete_all": {"cancel": "Cancel", "confirm": "Delete all", "content": "All the items will be delete permanent and can not recover. Do you want to continue?", "title": "Delete all"}, "delete_item_bin": {"permanent_delete": "Delete permanent", "recovery": "Recover"}, "delete_permanent": {"cancel": "Cancel", "confirm": "Delete", "content": "This items/folders will be delete permanent and can not recover. Do you want to continue?", "title": "Delete permanent"}, "delete_share": {"cancel": "Cancel", "confirm": "Delete", "file_content": "System will automatic remove your access permission of this file. Do you want to delete this file?", "file_title": "Delete File", "folder_content": "System will automatic remove your access permission of this folder. Do you want to delete this folder?", "folder_title": "Delete Folder"}, "file_not_exists": {"close": "Close", "content": "Sorry, your items you requested are not exist", "title": "Item are not exist"}, "move": {"cancel": "Cancel", "confirm": "Confirm", "content": "Members in new folder can access to this item. Current members will lost their access permission to this item", "title": "Move"}, "rename_folder": {"action": "Done", "title": "<PERSON><PERSON>"}, "restore": {"admin_content": "Cannot see the content because of the items has been deleted to the trash", "cancel": "Cancel", "close": "Close", "confirm": "Rest<PERSON>", "file_content": "Cannot see the content because of the items has been deleted to the trash", "file_not_exits_content": "Sorry, your items you requested are not exist", "title": "This item are in trash"}, "share_add_member": {"cancel": "Later", "confirm": "Cancel", "content": "Are you sure to cancel all your changes?", "title": "Cancel "}, "snack_bar": {"copy_link": "Link copied", "delete": "Deleted %1s item", "delete_all": "Deleted pernament", "delete_permanent": "Deleted pernament", "download": "Successfully Downloaded", "error": {"delete": "Something went wrong when delete item", "delete_all": "Something went wrong when delete all item", "move": "Something went wrong when move item", "recovery": "Something went wrong when restore item"}, "invite": "Access permission has been shared", "move": "Item has been moved successfully", "recovery": {"call_to_action": "Open in", "leading": "Item has been restored successfully", "tailing": "Back to original folder"}, "rename": "<PERSON><PERSON> successfully", "share": "Access permission has been shared", "share_edited": "Access permission has been changed", "transfer_owner": "Owner has been transfered successfully"}}, "search": {"hint": "Search"}, "share": {"appbar": {"title": {"add_member": "Add member", "done": "Done", "invite_member": "Invite member", "share": "Share"}}, "btn": {"add_member": "Add member ", "copy_link": "Copy link", "invite": "Invite member", "save": "Done"}, "common_permission": {"header": "", "restrict": "", "restrict_hint": "", "workspace_editor": "", "workspace_editor_hint": "", "workspace_viewer": "", "workspace_viewer_hint": ""}, "file_access": {"full": {"delete": "Remove access permission", "editor": "Can edit", "owner": "Owner", "viewer": "Can view"}, "short": {"editor": "Editor", "owner": "Owner", "viewer": "Viewer"}, "title": "Access permission"}, "headers": {"common_permission": "General access", "member_can_access": "Member with access"}, "in_workspace": "", "in_workspace_hint": "", "restrict": "Restricted", "restrict_hint": "Only member with access permission can open with the link", "send_noti": {"checkbox_send_title": "Send notification", "hint": "Add message"}}, "tab": {"bin_drive": "Trash", "my_drive": "My Drive", "share_drive": "Shared with me"}}, "error": {"cantOpenFile": "Can't open the file", "default_msg": "An error has occurred, please try again later", "empty": "No data", "error": {"400101": "Not enough photos taken. Please retake them.", "400102": "No suitable office found. Please contact the administrator.", "empty": "", "need_atleast_one_row": "", "no_precise_location": {"description": "Turn on \"Precise Location\" to get precise location", "title": "Allow access precise location"}}, "message_search_empty": "Sorry, there are no matching search results to show.", "noAppToOpen": "No app to open the file", "nodata": "No results", "title_search_empty": "No matching results", "unable_load_page_des": "An error has occurred. Please try again or recheck your network connection.", "unable_load_page_title": "Unable to load page", "wifi_connection": "There is no internet connection"}, "error_mapper": {"cancellation": "Request has been canceled", "decode_error": "Decoding error, we will check and resolve this issue soon", "network": "Unable to connect to the internet, please check your connection", "parse": {"error_mapping": "Data processing error, we will check and resolve this issue soon", "invalid_format": "Formatting error, we will check and resolve this issue soon", "invalid_source_format": "Source formatting error, we will check and resolve this issue soon"}, "server_defined": {"E400": "An error occurred, we will check and resolve this issue soon", "E401": "Unable to refresh token, please log out and log back into the app if this issue persists", "E404": "Url does not exist", "E500": "An error has occurred, we will check and resolve this issue soon", "E502": "Request cannot be processed yet, please try again later", "E503": "We are unable to process this request at the moment, please try again later", "E504": "We cannot process this request at the moment"}, "server_undefined": "An error occurred, we will check and resolve this issue soon", "timeout": "Unable to connect to the server after a period of time, please try again", "uncaught": "An error occurred, please try again!", "unknow": "Unknown error", "validation": {"empty": "No data available", "invalid_data": "Invalid input data", "invalid_data_type": "Input data is in an incorrect format"}}, "feature_info": {"can_not_download_file": "Downloading files and images from features on GapoWork is not allowed"}, "gift_detail": {"cancel_reason": "Cancellation reason", "delivery_address": {"address_hint": "You may skip the delivery address if it's not needed", "address_label": "Gift delivery address", "address_placeholder": "Enter delivery address", "confirm_button": "Confirm", "title": "Address"}, "description": "Description", "exchange_gift": "Redeem gift", "exchange_success": "", "gift_detail_exchange_success": "Gift redemption requested", "gift_detail_out_of_stock": "Out of stock", "gift_name": "Gift name", "images": "Image", "quantity": "Quantity", "remainning": "%s left", "required_points": "Required points"}, "gift_exchange": {"confirm_received": "Confirm gift received", "confirm_received_success": "Gift receipt confirmed", "receive_point": "Point recipient", "redeem_point_reason": "Refund reason"}, "gift_exchange_status": {"canceled": "Cancelled", "failed": "", "pending_approval": "Pending approval", "received": "Received", "waiting_receive": "Awaiting pickup"}, "loading": "Loading", "media_preview": {"could_not_prevew": "The format could not be previewed", "download": "Download", "download_sucessfully": "Download successfully", "download_unsucessfully": "Download failed", "downloading": "Downloading", "open": "Open"}, "memberPicker": {"assignee_search_text": "Search by name or email", "limit_description": "You can only select up to", "limit_title": "You have reached the limit of selection", "limit_user_description": "You have chosen to exceed the number of members that can be credited", "limit_user_title": "Maximum % members"}, "mini_app": {"emty_view": "Mini App helps you quickly access essential apps and pages."}, "notification": {"addAssignee": "% added assignee %", "addTime": "Add time", "addWatcher": "% added watcher %", "calendarChildNoti": {"attachments": "edited attachment of event", "description": "edited meeting description of event", "hasMeeting": "edited Google Meet of event", "inEvent": "in the event", "owner": "has transferred editing permisions to", "remindBefore": "edited remind setting of event", "room": "edited offline meeting of event", "scheduleType": "edited repeat setting of event", "time": "edited time of event", "title": "edited title of event"}, "changedStatus": "% changed task's status", "commented": "% commented", "commentedToATask": "% commented on the task", "deletedTask": "% deleted task", "edittedTask": "% updated task", "emptyTitle": "You don't have any notification", "endDate": "End date", "isNearlyExpired": " Task will be expired in {count}", "isOverDueDate": "Task is expired", "mentionedYouInAComment": "% mentioned you in a comment", "projectIsHidden": "Project is hidden", "snackbar_read_all_noti": "Mark all as read", "startDate": "Start date", "updatedDueDate": "% changed due date", "viewAllTasks": "View all tasks"}, "orgChart": "Organization chart", "portal": {"mini_app_title": "Mini App", "portal_add_link": "Add link", "portal_add_link_done": "Link added successfully", "portal_add_link_error": "Invalid link format", "portal_add_link_error_empty": "Please enter link name", "portal_add_link_error_empty_url": "Please enter URL", "portal_add_link_placeholder": "Attach URL", "portal_add_link_title": "Link name", "portal_add_link_title_placeholder": "Link name Example: Department document collection", "portal_add_link_url": "URL", "portal_add_onback_bottom_confirm_content": "Your unsaved changes will be lost.", "portal_add_onback_bottom_confirm_title": "Are you sure you want to exit?", "portal_add_title": "Add link", "portal_apply": "Confirm", "portal_cancel": "Cancel", "portal_delete_link": "Delete link", "portal_delete_link_confirm": "Are you sure you want to delete this link?", "portal_delete_link_done": "Link deleted successfully", "portal_edit_link": "Edit link", "portal_edit_link_done": "Edited link successfully", "portal_empty_title": "You can create links to important pages here", "portal_problem_with_server": "Cannot create/edit portal link", "portal_problem_with_server_close": "Understand", "portal_problem_with_server_description": "The system encountered an issue or the network connection is unstable. Please check again.", "portal_succes": "Done", "portal_title": "Portal", "portal_user_empty_view": "Your organization has no links yet.", "portal_warning_delete_description": "The content of the link cannot be undone.", "portal_warning_maximum_close": "Close", "portal_warning_maximum_description": "You can enter a maximum of 20 links.", "portal_warning_maximum_title": "Portal link restriction"}, "project_management": {"main": {"title": "Project management"}}, "reload_page": "Reload the page", "survey": {"actions": {"answered_surveys": "Answered Surveys", "continue": "Continue", "created_surveys": "Your Survey", "delete_survey": "Delete", "drafts": "Drafts", "edit": "Edit", "need_answer_surveys": "Unanswered survey", "review_answered": "View answered survey", "send": "Send"}, "answer": {"answer_label": "Answer", "enter_answer_placeholder": "Your answer...", "enter_your_answer_placeholder": "Your answer...", "no_options": "No options", "optional_label": "Optional", "required_label": "Required", "required_mark": "* Required"}, "builder": {"add_description": "Survey description", "add_survey_title": "Survey name", "compose_questions": "Compose questions"}, "choice": {"no": "No", "option_1": "Option 1", "option_2": "Option 2", "other": "Other choice", "other_short": "Other", "yes": "Yes"}, "common": {"answer_deadline": "Answer deadline", "cannot_access": "Cannot access", "cannot_access_msg": "Privacy changed or content is not available", "choose_period": "Select period", "enter_question": "Enter your question", "next_period": "Next", "no_data": "No data", "no_results": "No Result", "period": "Period:", "previous_period": "Previous", "remind_after_days_received": "{{days}} days after receiving the survey", "reminder": "Reminder", "selected_list": "Selected list", "send_feedback": "Send answer", "there_is_no_response": "There is no response", "view_more": "More"}, "count": {"count_answerer": "%s respondents", "count_survey_1": "You have ", "count_survey_2": " survey", "not_answer_title": "You have % survey to answer", "single_count_answerer": "%s respondent", "survey_list_title": "You have % survey"}, "defaults": {"add_user_error_message": "Error: Cannot add user", "best": "Best", "choose_time_reminder": "Choose reminder time", "copy_suffix": "(Copy)", "default_option": "Default option", "delete_confirm_message": "Are you sure you want to delete?", "delete_confirm_title": "Delete", "files_attached": "files attached", "format_label": "Format", "highest": "Highest", "lowest": "Lowest", "max_files_label": "Max files", "max_size_label": "Max size", "no": "No", "no_file_error_message": "Error: No file sent", "once": "Once time", "once_time_text": "Once time", "option_1": "Option 1", "option_2": "Option 2", "option_template": "Option {}", "other": "Other", "other_option": "Other option", "plural_responses_text": "%s responses", "points_format": "points", "recurring": "Repeat", "reminder_schedule": "Reminder schedule", "save_error_message": "Error saving", "single_response_text": "%s response", "untitled_question": "Untitled Question", "untitled_survey": "Untitled Survey", "upload_answer": "Answer with attachments", "worst": "Worst", "yes": "Yes"}, "detail": {"all_members": "All members", "anonymous_label": "Anonymous", "change_permission_title": "Change permission", "change_to_editor_permission": "Editor", "change_to_owner_permission": "Owner", "copy_link_button": "Copy link", "cycle": "Cycle:", "deadline": "Duration:", "editor_list_title_text": "Editor list", "editor_text": "Editor", "info_title": "Survey information", "mode_title": "Survey mode", "no_answers_yet": "No answers", "no_date": "No date", "no_name": "No name", "no_title": "No title", "owner_text": "Owner", "people_answered": "respondents", "period": "Period:", "period_label": "Period:", "plural_questions_count": "%s questions", "public_label": "Public", "remove_editing_permission": "Remove editing permission", "remove_editing_permission_description": "Editing permission will be updated after you finish editing the survey. You will not be able to edit this survey anymore.", "remove_editing_permission_title": "Remove editing permission", "repeat_label": "Repeat:", "respondent_text": "Respondent", "respondents": "Choose respondents", "score": "score", "share_with": "Share with", "single_question_count": "%s question", "title": "Survey detail", "transfer_ownership_description": "The new owner will receive a notification and can remove your access from this survey.", "transfer_ownership_title": "Transfer ownership", "transfer_title_button": "Transfer", "week_day_short": {"friday": "F", "monday": "M", "saturday": "S", "sunday": "S", "thursday": "T", "tuesday": "T", "wednesday": "W"}}, "dialogs": {"add_member_title": "Add respondent", "answer_confirm_desc_message": "Please click \"Send\" to complete", "answer_confirm_title_message": "Have you completed the survey?", "cancel_button": "Cancel", "choose_button": "<PERSON><PERSON>", "choose_date": "Choose date", "choose_time": "Choose time", "close": "Close", "complete_survey_content": "Your answer has been sent to questioner", "complete_survey_title": "Completed!", "confirm_button": "Confirm", "create_survey": "Create Survey", "delete_content": "Are you sure to delete survey", "delete_draft_content": "Are you sure you want to delete draft \"{{surveyName}}\"?", "delete_draft_title": "Delete draft", "delete_title": "Delete Survey", "exit_button": "Exit", "home_button": "Back to Home", "later_button": "Later", "monthly_sending": "Day of month", "must_have_2_answers": "Survey must have at least 2 answers", "must_have_answers": "Survey must have at least 1 answer", "no_user_message": "You don't have any friend or all of your friends are already invited.", "notification_title": "Notification", "pause_survey": "Pause", "resume_survey": "Resume survey", "save_button": "Save", "save_draft_content": "Do you want to save this survey?", "save_draft_title": "Save draft", "send_button": "Send", "send_reminder_button": "Send now", "send_reminder_content": "The system will send reminders to all participants who haven't completed the survey.", "send_reminder_title": "Send reminder", "update_button": "Update", "update_draft_content": "Do you want to update this survey?", "update_draft_title": "Update draft", "weekly_sending": "Day of week"}, "errors": {"draft_not_found": "Draft survey not found", "edit_screen_error": "Error opening edit screen", "generic": "An error occurred", "input_data_invalid": "inputData must be SurveyListParams", "load_draft": "Error loading draft", "load_draft_list": "Error loading draft survey list", "load_survey_history": "Cannot load survey history", "load_survey_info": "Cannot load survey information", "load_survey_list": "Error loading survey list", "save_survey": "Error saving survey", "search_drafts": "Error searching draft surveys", "search_surveys": "Error searching surveys"}, "frequency": {"title": "Frequency"}, "history": {"no_date": "No date", "title": "Survey histories"}, "incognito": {"hide_answered_detail": "Anonymous survey will not show details of members who answered.", "hide_unanswered_detail": "Anonymous survey will not show details of members who haven’t answered."}, "main": {"title": "Survey"}, "messages": {"create_description": "Create surveys to evaluate and learn more about the issues you care about", "draft_add_new_editor_message": "You have added editors to the draft \"%s\"", "draft_saved": "Draft saved", "draft_updated": "Draft updated", "editor_added": "You have added an editor to the survey", "empty_answered_survey_description": "You don't have any survey", "empty_draft_description": "You don't have any draft", "empty_not_answer_subtitle": "When you receive a notification about the survey, the content will appear here", "empty_not_answer_title": "You don't have any survey to answer", "empty_owner_subtitle": "Create surveys to evaluate and learn more about the issues you care about", "empty_owner_title": "You don't have any survey", "send_survey_error": "An error occurred while sending the survey", "survey_deleted": "Survey deleted"}, "question": {"copy_suffix": "(Copy)", "default_untitled": "Untitled question"}, "question_types": {"add_button": "Add", "add_user_answer_desc": "Add participants for this survey", "add_user_answer_title": "Participants", "chat_group_chat": "%s Group chat", "chat_group_chats": "%s Group chats", "chat_member": "%s Member", "chat_members": "%s Members", "create_question_survey": "Create questions", "hidden_feedback_survey_desc": "The identity of respondents will be displayed as anonymous", "hidden_feedback_survey_title": "Anonymous responses", "input_desc_survey_placeholder": "Add description", "input_title_survey_placeholder": "Add title", "link_feedback_survey_header": "Sharing and response", "reminder_selection_hour_many": "Remind before % hours", "reminder_selection_hour_one": "Remind before % hour", "reminder_selection_minute": "Remind before % minutes", "reminder_selection_not_reminder": "No remind", "reminder_selection_title": "Reminder", "select_type_survey": "Type of survey", "setup_survey": "Survey settings", "share_link_survey_desc": "The link will be available after you send survey", "share_link_survey_title": "Share link", "sharing_permission_header": "Edit permission", "sharing_permission_subtitle": "These people can change survey settings and edit questions.", "survey_display_name_me": "% (Me)", "survey_header_department": "%s Department", "survey_header_departments": "%s Departments", "survey_header_jobtitle": "%s Job title", "survey_header_jobtitles": "%s Job titles", "text_question": "Paragraph", "type_once_survey": "Once time", "type_repeat_survey": "Repeat", "type_survey_multi": "Checkboxes", "type_survey_once": "Multiple choice", "type_survey_point_level": "Linear scale", "type_survey_text": "Paragraph", "type_survey_time_answer_title": "Due date", "type_survey_upload_file": "Upload file"}, "questions": {"add_file": "Add file", "add_option": "Add options", "add_other": "add \"Other\"", "add_question": "Add question", "enter_title": "Survey question", "no_title": "Question has no title", "no_title_hint": "Question has no title", "not_selected": "Not selected", "option_1": "Option 1", "option_2": "Option 2", "or": "or", "question_number": "Question %", "question_type_selector": "Choose question type", "score_selected": "Score selected:"}, "respondents_page": {"display_name_me": "% (Me)", "remind_button": "Remind to answer", "remind_sent_success": "Reminder sent to all members who haven't responded!", "title": "Respondents", "user_tab_all": "All (%)", "user_tab_not_responded": "Not Responded (%)", "user_tab_responded": "Responded (%)"}, "scale": {"max_default": "Highest", "min_default": "Lowest", "score_pattern": "{value}/{max} points"}, "schedule": {"daily": "Daily", "duration": "Duration", "every_month": "Every month", "monthly": "Monthly", "monthly_start_time": "Monthly start time", "no_deadline": "No due date", "once": "Once", "start_time": "Start time", "weekly": "Weekly"}, "settings": {"add_button": "Add", "add_editor": "Add editors", "add_people_section": "ADD PEOPLE", "add_respondents": "Respondents", "anonymous_response_subtitle": "Respondent information will be shown anonymously.", "anonymous_response_title": "Anonymous responses", "share_level": {"public": "Public", "within_workspace": "Within the workspace"}}, "status": {"deleted": "Deleted", "draft": "Draft", "ended": "Completed!", "expired": "Past due", "paused": "Inactive", "running": "In Process"}, "steps": {"settings_title": "Survey settings", "type_title": "Type of survey"}, "success": {"answer_desc": "Your answer has been sent", "answer_title": "Completed!", "back_to_list_button": "Back to the survey list", "copy_link_successfully": "<PERSON>pied succesfully", "create_desc": "The survey has been sent to all respondents.", "create_title": "Successfully create survey", "edit_desc": "The survey has been sent to all respondents.", "edit_title": "Successfully update survey", "link_created_desc": "You can send your survey link to everyone in your workspace", "link_created_title": "Your survey have been created"}, "tabs": {"draft_title": "Draft", "need_answer": "Need to answer", "sent": "<PERSON><PERSON>", "sent_survey": "Sent survey"}, "upload": {"attachments_count": "{count} attachments", "file_formats": "Formats: {value}", "file_max_files": "Max {count} files", "file_max_size": "Max size: {value}", "instructions_default": "Answer with attachments"}, "validation": {"add_questions_before_continue": "Please add at least 1 question before continuing", "anonymous_public_warning": "Anonymous mode with public link may affect security", "check_draft_info": "Please check draft information", "check_recipient_info": "Please check recipient information", "check_survey_info": "Please check survey information", "choose_survey_type": "Please choose survey type before continuing", "description_min_length": "Description must be at least 5 characters", "duplicate_prevention_enabled": "Duplicate response prevention enabled automatically", "end_date_after_start": "End date must be after start date", "file_count_too_many": "Maximum number of files cannot exceed 10", "file_size_too_large": "Maximum file size cannot exceed 100MB", "hour_invalid": "Invalid hour (0-23)", "invalid_answer": "All question should be answered.", "invalid_step": "Invalid step", "max_choices_invalid": "Maximum choices cannot be greater than total options", "max_responses_positive": "Maximum responses must be greater than 0", "minute_invalid": "Invalid minute (0-59)", "monthly_day_invalid": "Invalid day of month (1-31)", "monthly_needs_day_list": "Monthly schedule must have day list (schedule.value required)", "monthly_schedule_needs_days": "Monthly schedule must select at least 1 day", "needs_recipients_or_public": "Must have survey recipients or enable public link", "no_public_link_recurring": "Cannot create public link with recurring survey", "option_empty": "Option cannot be empty", "question_empty": "Question unable to be empty", "question_index_empty": "Question {index} cannot be empty", "question_index_needs_option": "Question {index} must have at least 1 option", "question_needs_options": "Multiple choice question must have options", "question_too_long": "Question too long (max 500 characters)", "question_too_many_options": "Question has too many options (max 20)", "questions_max": "Survey can add 30 questions only", "questions_required": "Must have at least 1 question", "recurring_needs_time": "Recurring schedule needs send time", "response_limit_positive": "Response limit must be greater than 0", "scale_max_levels": "Scale cannot exceed 10 levels", "scale_min_less_than_max": "Min value must be less than max", "scale_needs_min_max": "Scale question must have min/max values", "schedule_needs_time": "Schedule must have send time", "submit_failed": "Submit survey failed.", "title_empty": "Title is empty, will use default title", "title_min_length": "Title must be at least 3 characters", "title_required": "Survey name cannot be empty", "title_too_long": "Survey name should not have more than 500 characters", "too_many_recipients": "Cannot send directly to more than 1000 people", "weekly_day_invalid": "Invalid day of week (0-6)", "weekly_needs_day_list": "Weekly schedule must have day list (schedule.value required)", "weekly_schedule_needs_days": "Weekly schedule must select at least 1 day", "workspace_recommendation": "Recommend selecting workspace for better management"}, "value_range": {"to": "to"}}, "survey_frequency_daily": "Send daily", "survey_frequency_monthly": "Send monthly", "survey_frequency_weekly": "Send weekly", "survey_respondent_answered": "Answered", "survey_respondent_in_progress": "In progress", "survey_respondent_invited": "Invited", "survey_respondent_no_name": "No name", "survey_respondent_not_answered": "Not answered", "survey_respondent_unknown": "Unknown", "task": {"activity": "Activity", "addDueDate": "Add due date", "addMoreAssignee": "Add assignee", "addPriority": "Add priority", "addTag": "Add tag", "addWatcher": "Add watcher", "adminCreateLimitDescription": "You have reached limit of this feature. Please upgrade your workspace on the website.", "all": "All", "allTag": "All tag", "archive": "Archive", "archived": "Archived", "areYouSureDeleteTag": "Are you sure to remove the label from this project?", "assignedByMe": "Created by me", "assignedToMe": "Assigned to me", "assignee": {"add": "Add", "assignMe": "Me", "assignees": "Assignee", "changSubtaskToTask": "Convert to task", "empty_assignee": "No one has been \nadded to the list yet", "follower": "Watcher", "list": "List", "list_filter": "Assignee list", "member": "Assignee", "remove_filter": "Remove filter", "save": "Save", "select": "Select", "select_member": "Select Members"}, "attachFile": "Attachment file", "attachFileCamera": "Take a photo", "attachFileChooseFile": "Choose <PERSON>", "attachFileChooseImage": "Choose Image", "attachFileChooseMedia": "Choose Media", "attachFileChooseVideo": "<PERSON>ose <PERSON>", "attachFileDownload": "Download", "attachFileRemove": "Remove file", "attachFileRemoveButtonTitle": "Remove", "attachFileRemoveMessage": "Removal cannot be undone. Are you sure you want to remove this file?", "attachFileScreenTitle": "Attachment file", "attachFileUpload": "Upload", "bottom_sheet_item_to_task_list": "Go to task list", "cant_find_task": "Cannot find task", "cant_performed_archived_task": "This action cannot be performed with an archived task", "changeSubTaskToTask": " Convert to task", "changed": "Converted ", "changedJob": " to a task", "commentHere": "Write a comment", "commentUploadError": "An Error occurred in the uploading", "commentUploadTryAgain": "Try again", "confirmCancelUploading": {"message": "If you back this screen, the uploading will be cancelled. Please wait until the upload be success", "no": "Yes", "title": "Wait for a second!", "yes": "Cancel"}, "confirmDeleteComment": "Are you sure want to delete this comment?", "confirmDelete_cancel": "Close", "confirmDelete_title": "Delete", "contentDeleteDependencies": "", "contentDeleteTaskDependency": "This task is depended on other tasks. Are you sure of deleting this task?", "contentDuplicateTaskDependency": "This task is having dependency settings so it is not impossible to move on another project. Gapowork will duplicate this task", "contentLogOutTagWhenBack": "If you exit, your content created will be canceled.", "contentYouSureExitEditTag": "If you exit, your edits will be cancelled.", "copiedCommentContent": "Comment content copied", "copiedTaskUrl": "Task URL copied", "copyComment": "Copy comment", "copyOf": "Copy of ", "createLimitTitle": "Cannot create anymore", "createNewTask": "Create a new task", "createTag": "Create tag", "createTask": "Create", "createTodoDone": "Done", "created": "has been created", "datetimePicker": {"add": "Add", "alertTimeInPast": "Time is invalid", "alertTimeInPastContent": "Time %1s can not before now!", "cancel": "Cancel", "choose": "<PERSON><PERSON>", "datePickerTitle": "Choose due date", "done": "Done", "duplicateCreateTime": "Hour creating duplicate task", "duplicateDate": "Date of the repeating series", "duplicateDueDate": "Duplicate task deadline", "duplicateTaskInclude": "Duplicate task include", "duplicateTime": "Time settings of duplicate task", "enterDate": "Enter date", "enterTime": "Enter time", "from": "from", "onMonthDay": "On day", "onWeekDay": "On week day", "pickDate": "Pick date", "repeat": "Repeat", "repeatEvery": "Repeat every", "repeatEveryYear": "Repeat every year", "repeatTime": "Repeat time", "taskRepeat": "Task repeat", "timePickerTitle": "Pick time", "watcher": "Watcher"}, "deleteComment": "Delete comment", "deleteDependencies": "Delete dependencies", "deleteTag": "Delete tag", "dependencies": "Dependencies", "descriptionHint": "Task description...", "detail": "Details", "dueDate": "Due date", "dueDate403": "You do not have permission to edit duedate", "editComment": "Edit comment", "editTag": "Edit tag", "editedTagSuccess": "The tag edited successfully", "exit": "Exit", "files": "Files", "filterTag": "Filtered tag", "filterTitle": "Task Filter", "filter_by_assignee": "Filtered by assignee", "folderMoved": "Folder moved", "hideProject": "Hide project", "ignoreActivityLogTitle": "Detail", "in": "Location", "in_section": "Section", "inputNameTag": "Enter a tag name", "listTag": "Tag list", "location": "Position", "longPress": {"accessDenied": "You do not have permission to delete this task!", "clone": "Duplicate", "confirmDeleting": "All content and history of this task will be deleted and cannot be recovered.\nDo you want to delete this task?", "copied": "Link copied", "copyUrl": "Copy link", "delete": "Delete", "deleteTask": "Delete", "deleted": "Task has been deleted!", "edit": "Edit", "subTaskDeleted": "Subtask has been deleted!", "view": "View details"}, "manageTag": "Tag management", "mediaPicker": {"errorLimitContent": "You can select only 1 image/video per comment", "errorLimitTitle": "Exceed allowances"}, "moveFolder": "Move folder", "moveTaskList": "Move task list", "my_task": "My task", "nextTasks": "Next task", "notificationSettings": "Notification settings", "notificationTitle": "Task notifications", "openTaskList": "Go to task list", "pickAssigneeDone": "Done", "pickAssigneeTitle": "Add assignee", "pickAssignees": "Select", "pick_color": "Pick color", "pickdueDate": "Select due date", "previousTasks": "Previous task", "priority": {"high": "High priority", "low": "Low priority", "medium": "Medium priority", "none": "None", "short": {"high": "High priority", "low": "Low", "medium": "Medium", "none": "None"}}, "projectNotTag": "The project has no tag yet.", "projectNotTagPlease": "This tag does not exist", "projectPickerTitle": "Choose position to save your task", "projectSearchHint": "Search project or folder or taskList...", "projectSectionPickerTitle": "Section", "readMore": "See more", "removeTag": "Delete tag", "repeatIncludeAttachmentTitle": "Attachment", "reply": "Reply", "replyComment": "Reply comment", "save": "Save", "saveEditTag": "Save changes", "search": "Search", "search_tag": "Search tag", "section": {"confirmDiscard": {"description": "Are you sure you want to discard this task? All data you have filled will not be saved", "discard": "Discard", "later": "Close", "title": "Discard this task?"}, "confirmRemove": {"cancel": "Cancel", "keepTask": "Delete this section and keep %1s tasks inside", "remove": "Delete", "removeSectionAnd": "Delete this section and %1s tasks inside", "title": "Delete this section?"}, "create": {"pickColor": "Choose color for new section", "setName": "Input section name here...."}, "createNewSection": "Create section", "createdSuccessfully": "Section has been created successfully", "more": {"edit": "Rename section", "remove": "Delete section"}, "move_to_section": "Task has been moved to section", "removeAllSuccessfully": "Inside sections and tasks are deleted", "removeSuccessfully": "Section is deleted", "titleAction": {"create": "Create", "save": "Save"}, "updateSuccessfully": "Section name has been changed"}, "selectPosition": "Select position", "selectPositionItem": "Select position", "selectTag": "Select tag", "showAsAProject": "Show as a project", "showLess": "Show less", "snackBarAttachFile": "File", "snackBarEmptyTodoTitle": "Please input the task name", "snackBarRemoved": "has been removed", "sort": {"byCreatedAt": "Sort by created date", "byDueDate": "Sort by due date", "byDueDateAsc": "Sort by duedate ascending", "byDueDateDesc": "Sort by duedate descending", "byPriority": "Sort by priority", "sort": "Sort task"}, "status": {"all": "All", "done": "Done", "haveChangedTo": "changed to", "inprogress": "In Progress", "status": "Status", "title": "Task", "todo": "To do"}, "status_v2": {"completed": "Completed", "markComplete": "Mark as completed"}, "tag": "Tag", "tagHaveNotBeenProject": "This tag added to the project", "tagPicked": "The tag is selected", "tagRemoveSuccess": "The tag removed successfully", "task": "Task", "taskContent": "Task description", "taskContentSave": "Save", "taskEmpty": "Content does not exist", "taskListMoved": "Task list moved", "taskListSearchHint": "Search tasklist", "taskNoAccess": "Or has no permission to access", "taskSaveCopy": "Save", "taskSearchPlaceHolder": "Search by task title", "taskSearchTitle": "Search tasks", "taskSelected": "Selected task", "taskTitle": "My task", "task_lowercase": "Task", "task_notifications": "task notifications", "task_of": "Task of", "task_subTask": "Subtask", "thisProjectIsNotShownInTaskManagement": "This project is not shown in task management", "thisProjectIsShownInTaskManagement": "This project is shown in task management", "titleHint": "Type task title...", "todo": "Subtask", "unarchive": "Rest<PERSON>", "understand": "Understood", "uploadFileSizeLimitContent": "File is too large. Please choose a file bellow 10 MB", "userCreateLimitDescription": "You have reached limit of this feature. Please contact workspace admin for support.", "viewMoreComments": "View more %1s comments", "viewMoreReplies": "View more %1s replies", "watching": "Watching", "whenMovedToAnotherProjectAllTasks": "When moved to another project, all tasks will lose information: “<PERSON>signee, Watcher, <PERSON>”"}, "taskCollab": {"addTask": "Add task", "alertDes": "This function will be applied to all members of the chat group and cannot be undone. Do you want to activate?", "cancel": "Cancel", "empty": {"actionTitle": "Create a task list", "desciption": "The folder and task list you create will appear here.", "title": "No task list here"}, "enable": "Enable", "enableCollabButton": "Enable collab task", "enableCollabDes": "Manage tasks easily with team members in the collaborative team", "enableCollabTitle": "Task Management in Collab group", "externalWorkspace": "External workspace", "header": "List", "intro_meeting": {"description_1": "Manage meetings easily with team members in the collaborative team", "description_2": "Initiate a new meeting quickly in the Collaboration group", "description_3": "Track meeting information about time, location or review notes and attachments easily", "title_1": "Meeting list", "title_2": "Create a new meeting", "title_3": "Meeting information"}, "intro_task": {"description_1": "Categorize tasks to separate task lists", "description_2": "View detail of tasks and statuses in folders", "description_3": "Create and customize your personal tasks easily within collaborative group", "title_1": "Task list", "title_2": "Task list", "title_3": "Add new task"}, "notiSetting": {"addAssignee": "Add assignee", "addWatcher": "Add watcher", "checkTaskDone": "Complete task", "deleteTask": "Delete task", "description": "A notification message will be sent to your collab group when you activate this.", "taskOverDue": "Overdue", "title": "Send notification message", "titleSelection": "Receive notification when", "upcoming": "Overdue soon"}, "notification_config": {"title": "Send Notification Message", "tooltip": "The system will send a notification message about the task to the collaboration group"}, "reasonCantEnableCollab": "This function can only be activated by collab group owner"}, "taskCreation": {"addContent": "Add description", "addDate": "Due date", "addDependencies": "Add dependencies", "addPerson": "Add person", "addPriority": "Priority", "addTodo": "Add", "addTodoButton": "Add task to checklist", "attachFile": "Attach file", "descriptionHint": "Enter task description", "draft": "Composing", "failure": {"emptyTaskTitle": "Please enter task title", "fileDoNotHasExtension": "Can not upload %1s files which does not have extension", "generalTitle": "Error", "max16AttachmentFilesMessage": "Up to 16 attachments can be uploaded, please try again.", "max16AttachmentFilesTitle": "Error", "uploadTitle": "An Error occurred in the uploading"}, "finish": "Save", "priority": "Priority", "todoHint": "Type task name here...", "upload": "Upload"}, "taskFolder": {"confirmDelete": {"accept": "Delete", "accessDenied": "You do not have permission to delete this folder!", "cancel": "Close", "content": "Are you sure want to delete this folder? Everything in this folder will be deleted permanently.", "contentWhenArchived": "Are you sure want to delete this folder? Everything in folder list will be deleted permanently", "deleted": "Folder has been deleted!", "title": "Delete"}, "create": "Create", "createAction": "Create", "createFolderSuccessfully": "The folder has been created successfully", "createHint": "Name the folder...", "createTitle": "Create new folder", "editAction": "Save", "emptyFolderContent": "This folder has no task list, let's create one!", "moreActions": {"add_folder": "Create folder", "add_tasklist": "Create task list", "archive": "Archive", "delete": "Delete", "rename": "<PERSON><PERSON>", "unarchive": "Rest<PERSON>"}, "title": "Folder", "updateFolderSuccessfully": "The folder has been updated successfully"}, "taskGeneral": {"defaultProjectName": "My Task", "isDone": "Done", "project": "Project", "show": "Show", "status": {"allTask": "All task", "doneToday": "Task need to be done today", "emptyDoneToday": "No tasks need to be done today.\nYou should take a coffee and make a plan for future!", "emptyNoDueDate": "No task without due date now.\nYou should take a rest and make a plan for future!", "emptyUpcoming": "No upcoming tasks now.\nYou should take a rest and make a plan for future!", "noDueDate": "Task with no due date", "overdue": "Overdue task", "upcoming": "Upcoming task"}}, "taskList": {"confirmDelete": {"accept": "Delete", "accessDenied": "You do not have permission to delete this task list!", "cancel": "Close", "content": "Are you sure want to delete this task list? Tasks in this task list will be deleted permanently.", "contentWhenArchived": "Are you sure want to delete this task list? Tasks in this task list will be deleted permanently.", "deleted": "Task list has been deleted!", "title": "Delete"}, "createAction": "Create", "createHint": "Name task list", "createTaskListButtonLabel": "Add new task list", "createTaskListSuccessfully": "The task list has been created successfully", "createTitle": "Create new task list", "editAction": "Save", "moreActions": {"add": "Create new task", "archive": "Archive ", "delete": "Delete ", "rename": "<PERSON><PERSON> ", "unarchive": "Rest<PERSON> "}, "noTaskDescription": "No task to show", "noTaskTitle": "There are no tasks", "title": "Task list", "updateTasklistSuccessfully": "The task list has been updated successfully"}, "taskProject": {"add": "Add", "addNameForProject": "Add a name for the project", "addNewMemberSuccessfully": "New member are added successfully", "addNewMembersSuccessfully": "New members are added successfully", "addNewProject": "Add new project", "archive": "Archive", "archive_later": "Later", "cancel": "Later", "collabEditMember": {"description": "This project is enabled from group chat. Chat group members will be updated.", "title": "Edit project member"}, "confirmDelete": {"accept": "Delete", "accessDenied": "You do not have permission to delete this project!", "cancel": "Close", "content": "Are you sure want to delete this project? Everything in this project will be deleted permanently.", "contentWhenArchived": "Are you sure want to delete this project? Everything in this project will be deleted permanently.", "deleted": "Project has been deleted!", "title": "Delete"}, "confirmLeave": {"collabDescription": "You cannot access tasks in this project and chat group. Are you sure you want to exit?", "description": "You cannot access tasks in this project. Are you sure you want to exit this project?", "discard": "Discard", "leave": "Exit", "title": "Exit project"}, "confirm_discard_project_editing": "Are you sure you want to discard the editing?\nThe modifications you have just made will not be saved.", "createNewProject": "Create new project...", "createProjectSuccessfully": "The project has been created successfully", "department": "Department", "discard": "Discard", "discard_editing": "Discard editing", "editProject": "Edit project", "edited_project_successfully": "Edited the project successfully", "emptyTaskListTitle": "There is no task list", "emptyTitle": "There is no project", "filter": {"all": "All", "collabProject": "Collabration group", "collabProjectHeader": "Collabration group project", "generalProject": "General project", "generalProjectHeader": "General project"}, "group_chat": "Group chat", "leaveProjectSuccessfully": "You have left the project", "member": "Member", "moreActions": {"add": "Create folder or task list", "addNewMember": "Add new member", "archive": "Archive", "delete": "Delete", "edit": "Edit", "leavingTheProject": "Exit project", "rename": "<PERSON><PERSON>", "unarchive": "Rest<PERSON>", "viewReport": "View report"}, "myTask": "My task", "name": "My project", "note1_invite_member": "Only members of this organization can be added.", "note2_invite_member": "Only members of this project can view and edit project tasks.", "note3_invite_member": "External members are removed automatically, only organization members are added.", "note_invite_member": "Only members of this project can view and edit project tasks.", "openProjectList": "Go to project list", "pin": {"pin": "Pin project", "pin_limit": "You can pin up to %s projects", "pin_success": "The project has been pinned", "unpin": "Unpin project", "unpin_success": "The project has been unpinned"}, "project": "Project", "projectEmpty": "Content does not exist", "projectNoAccess": "Or has no permission to access", "project_members": "Project's members", "report": "Report", "role": "Role", "setProjectColor": "Select a color for project", "setProjectIcon": "Select a icon for project", "setProjectName": "Name the project...", "titleAddMember": "Add member", "unarchive": "Rest<PERSON>", "updateProjectSuccessfully": "The project has been updated successfully"}, "test": "Test", "text": {"show_less": "", "show_more": "", "text": {"show_less": "", "show_more": "", "title": ""}}, "ticket": {"activity": {"detail": {"reason": "Reason", "requester": "Requester"}, "type_with_detail": {"additional_title": "Request for Additional Information", "back_step": "Move back to previous step", "onhold_title": "Request on Hold", "reopen_title": "Reopen Request", "spam_title": "Report Spam"}, "view_detail": "View Details"}, "category": {"request": "Request List", "waiting_for_archive": "Pending Archive Approval"}, "create": {"action_btn": "Submit", "create_success": "Request Submitted Successfully", "input": {"base_attachment": {"not_pick_data": {"hint": "No content added"}}, "datetime": {"am_pm": {"am": "Morning", "pm": "Afternoon", "title": "Select Period"}, "date_picker_title": "Select Date", "days": "days", "hours": "hours", "minutes": "minutes", "part_of_day_picker_title": "", "time_picker_title": "Add Time", "time_picker_title_hint": "Select Time", "validator": {"end_before_start": "End time must be later than start time", "start_after_end": "Start time must be earlier than end time"}}, "department": {"done_btn": "Add", "title": "Add Department"}, "media": {"add": "Add", "view_all": "View All"}, "member": {"done_btn": "Done", "title": "Add Member"}, "number": {"btn": {"done": "Done"}, "currency": {"hint": "Search", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "validator": {"empty": "This field cannot be empty", "format": "Invalid format", "max": "Cannot enter a value greater than %1s", "min": "Enter a value between %1s and %2s", "range": "Enter a value between %1s and %2s"}}, "priority": {"action": "Select", "popup_title": "Select Priority", "title": "Priority"}, "privacy": {"hint": "Privacy Mode\\nOn: Only you can see the request\\nOff: Team members in the department can see the request", "title": "Privacy Mode"}, "relative_request": {"action": "Add", "popup_pick": {"done_btn": "Done", "search_hint": "Search", "title": "Related Requests"}, "title": "Select Related Requests"}, "request": {"action": "Select", "cancel": "Cancel", "search": {"hint": "Search"}, "title": "Request Type"}, "show_less": "Show Less", "show_more": "Show More", "table": {"button": {"add_a_row": "Add a Row"}, "need_atleast_one_row": "The table must have at least one row", "row": "Row", "success": {"add_a_row": "Added a Row", "duplicate_a_row": "Duplicated a Row", "remove_a_row": "Removed a Row"}}, "title": {"edit_text": "Edit", "hint": "Enter Title", "no_answer": "No Answer", "no_required": "Not Required", "required": "Enter Information", "title": "Title", "validator": {"empty": "Title cannot be empty"}}, "validator": {"empty": "This field cannot be empty", "snackbar_empty": "Please fill in all required fields to submit the request"}}, "title": "Create Request"}, "datetime": {"am": "Morning", "am_shorten": "AM", "pm": "Afternoon", "pm_shorten": "PM"}, "details": {"actions": {"add_handler": "Add Assignee", "add_handler_title": "Add Assignee", "add_label": "Add Label", "add_watcher": "Manage followers", "approve": "Approve", "approve_archive": "Approve on-hold", "archive": "Move to on-hold", "back_last_step": "Reopen last steps", "back_step": "Go back to the previous step", "cancel": "Cancel ticket", "change_handler": "Change handler", "change_handler_title": "Change handler", "chat": "1-1 Chat", "close": "Close Ticket", "continue_handle": "Continue Handling", "copy": "Duplicate", "delete": "Delete Ticket", "edit": "Edit field", "manage_follower": "", "more": "More", "next_step": "Next Step", "no_approve": "Reject", "open": "Reopen Ticket", "provide_info": "Request to provide info", "reject_archive": "Reject Archive", "report_spam": "Spam", "unfollow": "Unfollow"}, "add_follower": {"all_steps": {"description": "Add or delete followers for all steps", "title": "For all steps"}, "current_step": {"description": "Add or delete followers for the current step", "title": "For the current step"}, "remove_all_steps": {"description": "", "title": ""}, "remove_current_step": {"description": "", "title": ""}, "success": "Updated followers successfully", "success_all_step": "Adding followers to all steps. Please wait a few minutes", "title": "Manage followers"}, "add_handler_success": "Added Assignee Successfully", "add_label_success": "Label Updated Successfully", "additional_request_edit": {"appbar": {"action_btn": "Send", "title": "Provide Additional Information"}, "body": {"attachments": {"action_btn": "Add", "title": "Attachments"}, "hint": "If new information needs to be provided, please enter it in the Additional Information section below.", "provide_info": {"hint": "Respond to the assignee", "title": "Additional Information"}}}, "appbar": {"rtf_title": "Content", "title": "Request Details"}, "approve_on_hold_success": "Successfully approved request on hold", "approve_success": "Approval successful", "back_last_step_popup": {"title": "Reopen last steps"}, "back_step_popup": {"back_success": "Request successfully backed to previous step", "reason": "Reason", "reason_max": "Information cannot exceed 200 characters", "reason_placeholder": "Enter reason", "select_step_empty": "You have not selected a step name", "step_back_to": "Step needs to be reprocessed", "submit": "Save", "title": "Go back to the previous step"}, "cancel_success": "Request cancellation successful", "change_handler_success": "Successfully reassigned handler", "close_success": "Request closed successfully", "comment_tabs": {"comment_hint": "Write a comment", "empty": "No comments available"}, "delete_success": "Request deleted successfully", "detail_tab": {"code": "Ticket number", "code_copied": "Ticket number copied", "customer_name": "Customer Name", "empty_data": "No responses available", "priority": "Priority", "provided_content": "Additional Information Provided", "response_content": "Response Content", "ticket_content": "Request Content", "ticket_relative": "Linked Requests", "type": "Request Type"}, "duplicate": {"appbar_title": "Create Request", "snackbar": {"success": "Request submitted successfully"}, "ticket_title": "(Copy)"}, "edit": {"action": "Save", "snackbar": {"success": "Information updated successfully"}, "title": "Edit Ticket", "validation": {"can_not_edit": "You do not have permission to edit this request content"}}, "fill_require_fields_error": "Please fill in all required fields to continue", "move_to_on_hold": {"action": "On Hold", "hint": "Enter the reason", "label": "Reason", "success": "Successfully marked as spam", "title": "Request On Hold", "validator": {"empty": "This field cannot be empty"}}, "next_step_success": "Successfully processed", "on_hold_need_approve": "The request will be moved to on-hold status after approval", "on_hold_success": "Request successfully put on hold", "provide_info": {"response_btn": "Respond", "title": "Request for Additional Information"}, "reject_on_hold_success": "Successfully rejected request on hold", "reject_success": "Step rejection successful. The request has been closed.", "reopen": {"action": "Send", "title": "Reopen Request"}, "reopen_success": "Request reopened successfully", "report_spam_success": "Spam report submitted successfully", "request_to_provide_info": {"action": "Send", "hint": "Enter the reason", "label": "Additional Information Required", "success": "Request for additional information submitted successfully", "title": "Provide Additional Information", "validator": {"empty": "This field cannot be empty"}}, "review": {"placeholder": "Feedback (if any)", "reopen_btn_p1": "Or", "reopen_btn_p2": "Reopen the request", "reopen_btn_p3": "if necessary", "submit_btn": "Submit feedback and close the request", "title": "Service Quality Review"}, "search_assignee_placeholder": "Search", "sla": {"actual": "Reality", "assignee": "Handler", "branch": "Branch", "complete": "Complete", "day": "day", "detail": "Detail", "first_response": "Response time", "follower": "Follower", "hour": "h", "info": {"follower": "Follower", "handler": "Handler", "label": "Label", "supporter": "Supporter", "title": "Ticket information"}, "minute": "m", "no_item": "Not yet", "no_response": "No response", "node_actual_time": "Resolve time actual", "overdue": "Overdue", "parallel": "Parallel step", "popup": {"collab": "Group chat", "department": "Department", "member": "Member", "role": "Role"}, "remaining": "End in", "resolve_time": "Resolve time", "second": "s", "submited": "Submited", "supporter": "Supporter", "tag": "Label", "title": "Progress and SLA"}, "spam": {"action": "Send", "hint": "Enter the reason", "label": "Reason", "success": "Successfully marked as spam", "title": "Report Spam", "validator": {"empty": "This field cannot be empty"}}, "tabs": {"activity": "Activity Log", "comment": "Comments", "detail": "Details", "sla": "Progress and SLA"}, "tag": {"done_btn": "Done"}, "unfollow_success": "Successfully unfollowed the request"}, "filter_screen": {"add": "Add", "add_with_icon": " + Add", "apply": "Apply", "creator": "Creator", "end_date": "To", "end_day": "To", "filter": "Select date", "filter_pick_create": "Creator", "filter_pick_date_warning": "Invalid time range", "filter_pick_date_warning_2": "Please select a date range within %1s months.", "filter_pick_handler": "Assignee", "filter_pick_label": "Label", "filter_pick_member": "Add member", "filter_pick_member_done": "Done", "filter_pick_workflow_done": "Done", "filter_v2": "Filter", "implementer": "Implementer", "label": "Label", "priority": "Priority", "request_status": "Status", "request_type": "Ticket type", "reset_filter": "Reset", "select": "Select", "start_date": "From", "start_day": "From", "step_status": "Step status", "time_range": "Time"}, "home_menu": {"archive_created_by_me": "Created by <PERSON>", "archive_send_to_me": "Sent to Me", "created_by_me": "Created by <PERSON>", "created_by_my_department": "Created by My Department", "flow_management": "Processes I Manage", "follow": "I Follow", "menu_title": "<PERSON><PERSON>", "no_handler": "No Handler Assigned", "send_to_me": "Sent to Me", "send_to_my_assignee": "Sent to My Employees", "send_to_my_group": "Sent to My Group", "tabs": {"archive_created_by_me": {"approved": "Approved", "not_approve": "Not Approved", "waiting_for_approve": "Waiting for <PERSON><PERSON><PERSON><PERSON>"}, "archive_send_to_me": {"approved": "Approved", "not_approve": "Not Approved", "waiting_for_approve": "Waiting for <PERSON><PERSON><PERSON><PERSON>"}, "created_by_me": {"all": "All", "archived": "On Hold", "cancelled": "Cancelled", "closed": "Closed", "handled": "Handled", "handling": "Handling", "out_of_date": "Overdue", "spam": "Spam", "waiting_for_handling": "Waiting for Handling"}, "created_by_my_department": {"all": "All", "archived": "On Hold", "cancelled": "Cancelled", "closed": "Closed", "handled": "Handled", "handling": "Handling", "out_of_date": "Overdue", "spam": "Spam", "waiting_for_handling": "Waiting for Handling"}, "flow_management": {"all": "All", "approved": "Approved", "archived": "On Hold", "cancelled": "Cancelled", "handled": "Handled", "handling": "Handling", "not_approve": "Rejected", "out_of_date": "Overdue", "spam": "Spam", "waiting_for_archive": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "waiting_for_handling": "Waiting for Handling", "waiting_for_provide_info": "Waiting for provide info"}, "follow": {"all": "All", "archived": "On Hold", "cancelled": "Cancelled", "closed": "Closed", "handled": "Handled", "handling": "Handling", "out_of_date": "Overdue", "spam": "Spam", "waiting_for_handling": "Waiting for Handling"}, "no_handler": {"all": "All", "out_of_date": "Overdue"}, "search_by_code": "Code", "search_by_name": "Name", "search_hint": "Search by name, code", "send_to_me": {"all": "All", "approved": "Approved", "archived": "On Hold", "cancelled": "Cancelled", "handled": "Handled", "handling": "Handling", "not_approve": "Rejected", "out_of_date": "Overdue", "spam": "Spam", "waiting_for_archive": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "waiting_for_handling": "Waiting for Handling", "waiting_for_provide_info": "Waiting for provide info"}, "send_to_my_assignee": {"all": "All", "approved": "Approved", "archived": "On Hold", "cancelled": "Cancelled", "handled": "Handled", "handling": "Handling", "not_approve": "Rejected", "out_of_date": "Overdue", "spam": "Spam", "waiting_for_archive": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "waiting_for_handling": "Waiting for Handling", "waiting_for_provide_info": "Waiting for provide info"}, "send_to_my_group": {"all": "All", "approved": "Approved", "archived": "On Hold", "cancelled": "Cancelled", "handled": "Handled", "handling": "Handling", "not_approve": "Rejected", "out_of_date": "Overdue", "spam": "Spam", "waiting_for_archive": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "waiting_for_handling": "Waiting for Handling", "waiting_for_provide_info": "Waiting for provide info"}}}, "main": {"title": "Send to me"}, "node": {"item": {"waiting_for_archive": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "waiting_for_provide_info": "Waiting for provide info"}, "status": {"approved": "Approved", "archived": "Archived", "cancelled": "Cancelled", "handled": "Handled", "handling": "Handling", "not_approve": "Rejected", "not_turn_yet": "Not turn yet", "rejected": "Closed", "skipped": "Skipped", "spam": "Spam", "waiting_for_archive": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "waiting_for_handling": "Waiting for Handling", "waiting_for_provide_info": "Waiting for provide info"}}, "popup": {"confirm": {"approve": {"content": {"first": "Are you sure you want to approve the step ", "second": "? The request will be marked as handled or automatically move to the next step (if any)."}, "left_btn": "Cancel", "right_btn": "Approve", "title": "Approve Request"}, "approve_archive": {"content": {"first": "Are you sure you want to move the request step ", "second": "to On Hold?"}, "left_btn": "Cancel", "right_btn": "Approve", "title": "Confirm Request On Hold"}, "cancel_ticket": {"content": {"first": "Are you sure you want to cancel the request", "second": "? Once canceled, related data might be affected."}, "left_btn": "Decide Later", "right_btn": "Cancel Request", "title": "Cancel Request"}, "close": {"content": "Are you sure you want to close this request? Once closed, it cannot be reopened.", "left_btn": "Cancel", "right_btn": "Close Request", "title": "Close Request"}, "continue_handling": {"content": {"first": "Do you want to continue handling the request", "second": "?"}, "left_btn": "Cancel", "right_btn": "Approve", "title": "Continue Handling Request"}, "delete": {"content": {"first": "Are you sure you want to delete the request", "second": "? Once canceled, related data might be affected."}, "left_btn": "Decide Later", "right_btn": "Delete Request", "title": "Delete Request"}, "move_to_on_hold": {"content": {"first": "Are you sure you want to approve the step ", "second": "? The request will be marked as handled or automatically move to the next step (if any)."}, "left_btn": "Cancel", "right_btn": "Approve", "title": "Confirm Request On Hold"}, "no": "No", "reject": {"content": {"first": "Are you sure you want to reject the step ", "second": "Once rejected, the request will be closed."}, "left_btn": "Cancel", "right_btn": "Reject", "title": "Disapprove"}, "reject_archive": {"content": {"first": "Do you want to reject putting the request on hold", "second": "?"}, "left_btn": "Cancel", "right_btn": "Disapprove", "title": "Reject Request On Hold"}, "unfollow": {"content": "Are you sure you want to unfollow? Once unfollowed, you will no longer see the request.", "left_btn": "Cancel", "right_btn": "Unfollow", "title": "Unfollow Request"}, "update_node_status": {"content": {"first": "Have you completed the step ", "second": "? The request will be marked as handled or automatically move to the next step (if any)."}, "left_btn": "Cancel", "right_btn": "Update status", "title": "Confirm Completion of Step"}}, "info": {"no_label": {"content": "No labels have been applied to this request type. Please contact the administrator.", "left_btn": "Understood", "title": "No Labels"}, "no_workflow": {"content": "Your organization does not have any request types set up. Please contact the administrator.", "left_btn": "Understood", "title": "No Request Types Set Up"}}, "label": {"done_btn": "Done", "search_hint": "Search", "title": "Label"}}, "priority": {"high": "High", "low": "Low", "medium": "Medium", "urgent": "<PERSON><PERSON>"}, "search_screen": {"hint": "Type keywords and press Done \\n to start your search"}, "status": {"archived": "On Hold", "cancelled": "Cancelled", "closed": "Closed", "deleted": "", "handled": "Handled", "handling": "Handling", "spam": "Spam", "unknow": "", "waiting_for_handling": "Waiting for Handling"}}, "time": {"days_after_many": "% days after", "days_after_one": "% day after", "days_after_zero": "% day after", "days_ago_many": "% days ago", "days_ago_one": "% day ago", "days_ago_zero": "% day ago", "hours_after_many": "% hours after", "hours_after_one": "% hour after", "hours_after_zero": "% hour after", "hours_ago_many": "% hours ago", "hours_ago_one": "% hour ago", "hours_ago_zero": "% hour ago", "just_in_time": "Just in time", "just_now": "Just now", "minute_many": "% minutes", "minute_one": "% minute", "minute_zero": "% minute", "minutes_after_many": "% minutes after", "minutes_after_one": "% minute after", "minutes_after_zero": "% minute after", "minutes_ago_many": "% minutes ago", "minutes_ago_one": "% minute ago", "minutes_ago_zero": "% minute ago", "months_after_many": "% months after", "months_after_one": "% month after", "months_after_zero": "% month after", "months_ago_many": "% months ago", "months_ago_one": "% month ago", "months_ago_zero": "% month ago", "short": {"day": "day", "hour": "h", "minute": "m", "second": "s"}, "today": "Today", "tomorrow": "Tomorrow", "weeks_after_many": "% weeks after", "weeks_after_one": "% week after", "weeks_after_zero": "% week after", "weeks_ago_many": "% weeks ago", "weeks_ago_one": "% week ago", "weeks_ago_zero": "% week ago", "years_after_many": "% years after", "years_after_one": "% year after", "years_after_zero": "% year after", "years_ago_many": "% years ago", "years_ago_one": "% year ago", "years_ago_zero": "% year ago", "yesterday": "Yesterday"}, "timeKeeping": {"ableToCheckIn": "Able to check in from", "ableToCheckOut": "Able to check out to", "afternoon": "Afternoon", "approved": "Approved", "at": "", "attachment_text": "%s attachment", "attachments_text": "%s attachments", "cancel": "<PERSON><PERSON>", "canceled": "Canceled", "cantGetLocationDescription": "Cannot detect location. Please try again!", "cantGetLocationTitle": "Check in fail as this device cannot get your current location", "checkIn": "Check in", "checkInAt": "Check in", "checkInBefore": "Check in before", "checkInCheckOutTime": "Check in/Check out", "checkInSuccessed": "You have already checked in", "checkInWithoutShift": "Check in", "checkOut": "Check out", "checkOutAt": "Check out", "contentCameraSetting": "Allow GapoWork access your camera for taking photo", "contentLocationSetting": "Allow GapoWork access your location for timekeeping", "done": "Done", "earlyOut": "Early out", "endTime": "End", "errorWifiConnection": "Not connected to the wifi", "explanation_button_title": "Explanation", "explanation_form_disabled_text": "The explanation form has not been issued. Please contact the administrator if you need to submit an explanation.", "explanation_state_description": {"approved": "Explanation request approved", "canceled": "Explanation request canceled", "created": "Explanation request pending approval", "declined": "Explanation request denied", "unknown": "Unknown"}, "flexHours": "hours", "flexTimeHour": "Flex shift", "fromDate": "From date", "haveRequest": "Have request", "holiday": "Holiday", "holiday_list": "List of holidays", "hour": "hours", "lastCheckOut": "Latest check out", "lateIn": "Late in", "lateInEarlyOut": "Late in/Early out/Forget check in/out", "lateInLeaveEarly": "Late in/Early out", "minute": "minutes", "morning": "Morning", "noRequest": "There is no request", "noShiftName": "No shifts assigned", "noShiftToday": "You have no shift today", "notCheckIn": "Not check in", "notCheckOut": "Not checkout", "notFoundShift": "We're not found your shift", "notRecorded": "Recorded as absence", "notSetLocation": "Your organization has not set location for timekeeping", "number": "No", "on": "", "onTime": "On time", "openSetting": "Open setting", "outOfCheckInTime": "Cannot check in because out of check in time", "pending": "Pending", "permission": {"disable_device": "Cannot check in/check out because the administrator has deactivated your device", "disable_device_descrition": "In case you use more than one device, please contact your administrator to activate it.", "unregister_device": "Cannot check in/check out because you are not using the registered device", "unregister_device_description": "In case you use more than one device, please contact your administrator to activate it.", "use_other_device": "Cannot check in/check out because you are using the same device with another", "use_other_device_description": "Cannot check in/check out because you are not using the registered device"}, "popup": {"400101": "Not enough photos taken. Please retake them.", "400102": "No suitable office found. Please contact the administrator.", "can_not_get_location": {"description": {"ios_close": "Close", "ios_cta": "View instructions", "primary_span": "Please refer to the instructions via", "secondary_span": " this link ", "tertiary_span": "for better accuracy."}, "sub_descripton": "Or you can also gently shake your phone to calibrate the compass, which helps improve GPS accuracy.", "title": "Unable to determine the clock-in location"}, "exceed_time": "The time to take clock-in photos cannot exceed 5 minutes.", "no_precise_location": {"description": "Turn on \\\"Precise Location\\\" to get precise location", "title": "Allow access precise location"}, "not_enough_images": "Number of image when checkin is %1s"}, "rejected": "Rejected", "requestReason": "Reason", "requestTab": "Request", "shift": "Shift", "shiftCode": "Shift code", "shiftDetail": "Shift detail", "shiftFirstHalf": "First half", "shiftLaterHalf": "Later half", "shiftName": "Shift", "shiftTab": "Shift", "startTime": "Start", "statistics": {"lateInLeaveEarly": "Late in/Leave early", "leave": "Leave", "workingDay": "Working day"}, "successCheckInTitle": "Check in successfully", "successCheckOutTitle": "Check out successfully", "tab": {"approval": "Approval", "timeKeeping": "Time keeping", "timeSheet": "Time sheet", "workSheet": "Work sheet"}, "timeKeepingLogTab": "Timekeeping log", "title": "TimeKeeping", "titleCameraSetting": "GapoWork would like to access your camera", "titleLocationSetting": "GapoWork would like to access your location", "toDate": "To date", "today": "today", "today_is": "Today is", "totalWorkingTime": "Working time", "totalWorkingTimeText": "Total working time", "tryAgain": "Try again", "unavailableCheckIn": "Not yet time to check in", "understood": "OK", "workingHour": "Working hour", "youHave": "You have"}, "validation": {"permTaskUpdateContent": "This task is not a part of your organization. Please switch to another organization and try again later.", "permTaskUpdateTitle": "You do not have permission to edit this task", "permTaskViewTitle": "You do not have permission to view this task", "taskDescription": "Please enter task description", "taskTitle": "Please enter task title"}, "view_all": "View all", "view_less": "Less", "view_more": "More"}